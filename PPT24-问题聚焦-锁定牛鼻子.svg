<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 背景装饰 -->
  <defs>
    <linearGradient id="focusGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c5aa0;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#4a90e2;stop-opacity:0.05" />
    </linearGradient>
  </defs>
  
  <rect width="1920" height="1080" fill="url(#focusGradient)"/>
  
  <!-- 模块标题 -->
  <text x="200" y="80" font-family="Microsoft YaHei" font-size="28" fill="#666666">
    模块一 | 战略解码与机会洞察
  </text>
  
  <!-- 章节标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="#2c5aa0">
    第三节：问题聚焦
  </text>
  
  <!-- 核心Slogan -->
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="40" fill="#4a90e2">
    找到那个"四两拨千斤"的牛鼻子
  </text>
  
  <!-- 装饰弧线 -->
  <path d="M300,350 Q960,400 1620,350" stroke="#4a90e2" stroke-width="4" fill="none" opacity="0.7"/>
  
  <!-- 放大镜图标 -->
  <circle cx="960" cy="500" r="80" fill="none" stroke="#4a90e2" stroke-width="6"/>
  <circle cx="960" cy="500" r="50" fill="#4a90e2" opacity="0.1"/>
  <line x1="1020" y1="560" x2="1060" y2="600" stroke="#4a90e2" stroke-width="8"/>
  
  <!-- 核心引用 -->
  <rect x="300" y="650" width="1320" height="200" rx="15" fill="#f8f9fa" stroke="#4a90e2" stroke-width="2"/>
  
  <text x="960" y="720" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#2c5aa0" font-style="italic">
    "如果给我一个小时来解决一个生死攸关的问题，
  </text>
  <text x="960" y="770" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#2c5aa0" font-style="italic">
    我会花55分钟来定义它。"
  </text>
  <text x="960" y="820" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#666666">
    —— 阿尔伯特·爱因斯坦
  </text>
  
  <!-- 装饰元素 -->
  <circle cx="200" cy="400" r="8" fill="#4a90e2" opacity="0.6"/>
  <circle cx="1720" cy="450" r="12" fill="#6bb6ff" opacity="0.5"/>
  <circle cx="150" cy="700" r="6" fill="#4a90e2" opacity="0.7"/>
  <circle cx="1770" cy="750" r="10" fill="#6bb6ff" opacity="0.6"/>
</svg>
