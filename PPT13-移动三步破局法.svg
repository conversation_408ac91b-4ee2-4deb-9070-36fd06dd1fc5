<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#2c5aa0">
    移动的"三步破局法"
  </text>
  
  <!-- 第一步 -->
  <rect x="100" y="200" width="500" height="250" rx="15" fill="#e8f4fd" stroke="#4a90e2" stroke-width="3"/>
  
  <circle cx="150" cy="230" r="20" fill="#4a90e2"/>
  <text x="150" y="238" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">1</text>
  
  <text x="350" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2c5aa0">
    第一步：生产环节
  </text>
  <text x="350" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#4a90e2">
    配上"私人保健医生"
  </text>
  
  <text x="120" y="320" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#666666">方案：</text>
  <text x="120" y="350" font-family="Microsoft YaHei" font-size="18" fill="#666666">5G + 物联网传感器 + AI农业大脑</text>
  
  <text x="120" y="390" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#27ae60">效果：</text>
  <text x="120" y="420" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#27ae60">精品果率提升 15%</text>
  
  <!-- 第二步 -->
  <rect x="710" y="200" width="500" height="250" rx="15" fill="#e8f4fd" stroke="#4a90e2" stroke-width="3"/>
  
  <circle cx="760" cy="230" r="20" fill="#4a90e2"/>
  <text x="760" y="238" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">2</text>
  
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2c5aa0">
    第二步：品控环节
  </text>
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#4a90e2">
    配上"AI质检员"
  </text>
  
  <text x="730" y="320" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#666666">方案：</text>
  <text x="730" y="350" font-family="Microsoft YaHei" font-size="18" fill="#666666">5G + AI视觉分拣流水线</text>
  
  <text x="730" y="390" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#27ae60">效果：</text>
  <text x="730" y="420" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#27ae60">分拣效率提升 8倍</text>
  
  <!-- 第三步 -->
  <rect x="1320" y="200" width="500" height="250" rx="15" fill="#e8f4fd" stroke="#4a90e2" stroke-width="3"/>
  
  <circle cx="1370" cy="230" r="20" fill="#4a90e2"/>
  <text x="1370" y="238" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">3</text>
  
  <text x="1570" y="250" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2c5aa0">
    第三步：销售环节
  </text>
  <text x="1570" y="280" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#4a90e2">
    配上"数字身份证"
  </text>
  
  <text x="1340" y="320" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#666666">方案：</text>
  <text x="1340" y="350" font-family="Microsoft YaHei" font-size="18" fill="#666666">一物一码产品溯源 + 5G慢直播</text>
  
  <text x="1340" y="390" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#27ae60">效果：</text>
  <text x="1340" y="420" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#27ae60">线上售价高出同类 30%-50%</text>
  
  <!-- 连接箭头 -->
  <path d="M600,325 L710,325" stroke="#4a90e2" stroke-width="4" marker-end="url(#arrowhead)"/>
  <path d="M1210,325 L1320,325" stroke="#4a90e2" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="15" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#4a90e2"/>
    </marker>
  </defs>
</svg>
