<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#2c5aa0">
    核心能力一：算力网络 - "数字时代的发电厂"
  </text>
  
  <!-- 核心比喻区域 -->
  <rect x="200" y="150" width="1520" height="300" rx="15" fill="#f8f9fa" stroke="#4a90e2" stroke-width="2"/>
  
  <!-- 过去 -->
  <text x="300" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2c5aa0">过去：</text>
  <text x="300" y="240" font-family="Microsoft YaHei" font-size="24" fill="#666666">企业要用电 → 自己买发电机</text>
  <text x="300" y="270" font-family="Microsoft YaHei" font-size="20" fill="#999999">(成本高、维护难)</text>
  
  <!-- 现在 -->
  <text x="300" y="320" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2c5aa0">现在：</text>
  <text x="300" y="360" font-family="Microsoft YaHei" font-size="24" fill="#666666">企业要用电 → 拉根电线，按电表付费</text>
  
  <!-- 未来 -->
  <text x="300" y="410" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#4a90e2">未来：</text>
  <text x="300" y="450" font-family="Microsoft YaHei" font-size="24" fill="#4a90e2">企业要算力 → 用移动云，按需付费</text>
  
  <!-- 价值主张区域 -->
  <rect x="200" y="500" width="1520" height="200" rx="15" fill="#e8f4fd" stroke="#4a90e2" stroke-width="3"/>
  
  <text x="960" y="550" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2c5aa0">
    对客户的价值主张：
  </text>
  
  <text x="960" y="600" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#666666">
    "刘总，您再也不用花几十万自己买服务器、建机房了。
  </text>
  <text x="960" y="640" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#666666">
    把您的IT部门'外包'给我们移动！我们帮您把沉重的'固定资产'，
  </text>
  <text x="960" y="680" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#666666">
    变成灵活的'可变费用'！"
  </text>
  
  <!-- 装饰元素 -->
  <circle cx="1600" cy="200" r="40" fill="#4a90e2" opacity="0.1"/>
  <circle cx="1600" cy="200" r="25" fill="#4a90e2" opacity="0.3"/>
  <circle cx="1600" cy="200" r="10" fill="#4a90e2"/>
  
  <circle cx="1600" cy="350" r="35" fill="#6bb6ff" opacity="0.1"/>
  <circle cx="1600" cy="350" r="20" fill="#6bb6ff" opacity="0.3"/>
  <circle cx="1600" cy="350" r="8" fill="#6bb6ff"/>
</svg>
