<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3c72;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2a5298;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f2027;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="techGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#0066cc;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 科技感装饰元素 -->
  <circle cx="200" cy="200" r="100" fill="url(#techGlow)" opacity="0.6"/>
  <circle cx="1720" cy="880" r="150" fill="url(#techGlow)" opacity="0.4"/>
  
  <!-- 数据流线条 -->
  <path d="M100,300 Q400,200 700,350 T1300,280" stroke="#00d4ff" stroke-width="3" fill="none" opacity="0.7"/>
  <path d="M200,600 Q600,500 1000,650 T1600,580" stroke="#0099ff" stroke-width="2" fill="none" opacity="0.5"/>
  <path d="M50,800 Q450,700 850,850 T1450,780" stroke="#66ccff" stroke-width="2" fill="none" opacity="0.4"/>
  
  <!-- 主标题 -->
  <text x="960" y="400" text-anchor="middle" font-family="Microsoft YaHei" font-size="120" font-weight="bold" fill="white">
    数智驱动，决胜县域
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="520" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#e0e0e0">
    中国移动省公司区县经理"AI+"战略落地行动坊
  </text>
  
  <!-- 装饰弧线 -->
  <path d="M400,600 Q960,650 1520,600" stroke="#00d4ff" stroke-width="4" fill="none" opacity="0.8"/>
  
  <!-- 底部信息 -->
  <text x="200" y="950" font-family="Microsoft YaHei" font-size="32" fill="#cccccc">
    [公司LOGO]
  </text>
  <text x="1720" y="950" text-anchor="end" font-family="Microsoft YaHei" font-size="32" fill="#cccccc">
    [培训日期]
  </text>
</svg>
